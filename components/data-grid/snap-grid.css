/**
 * SnapGrid CSS Styles
 * Comprehensive styling for the data grid component
 * Following the design system patterns from snap-charts.css and tokens.css
 *
 * Features:
 * - Responsive design with mobile support
 * - Dark mode compatibility
 * - Virtual scrolling optimizations
 * - Performance optimizations
 *
 * @version 1.0.0
 */

/* ==========================================================================
   Global Custom Components
   ========================================================================== */



/* Snap Dropdown Styles - Matching Dashboard Style */
.snap-dropdown {
    position: relative;
    cursor: pointer;
    user-select: none;
    width: 100%;
}

.snap-dropdown .dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    height: 40px;
    border: 1.5px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    box-sizing: border-box;
    font-family: 'Amazon Ember', sans-serif;
    font-weight: 500;
    font-size: 12px;
    color: var(--text-primary);
    cursor: pointer;
}

.snap-dropdown.focused .dropdown-header {
    border-color: var(--action-btn-bg);
}

.snap-dropdown .dropdown-header span {
    flex: 1;
    text-align: left;
}

.snap-dropdown .dropdown-header img {
    width: 15px;
    height: 15px;
    flex-shrink: 0;
    /* Removed rotation transition and transform */
}

.snap-dropdown .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--bg-primary);
    border: 1.5px solid var(--border-color);
    border-radius: 8px;
    margin-top: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: var(--z-dropdown) !important;
    box-sizing: border-box;
    max-height: 200px;
    overflow-y: auto;
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

/* Webkit scrollbar styling for dropdown menu */
.snap-dropdown .dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.snap-dropdown .dropdown-menu::-webkit-scrollbar-track {
    background: transparent;
}

.snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: var(--text-primary);
}

.snap-dropdown .dropdown-menu.hidden {
    display: none;
}

.snap-dropdown .dropdown-menu:not(.hidden) {
    display: block;
}

.snap-dropdown .dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    font-family: 'Amazon Ember', sans-serif;
    font-weight: 500;
    font-size: 12px;
    color: var(--text-primary);
    transition: background-color 0.2s ease;
}

.snap-dropdown .dropdown-item:hover {
    background: #F3F4F6;
}

.snap-dropdown .dropdown-item:first-child:hover {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.snap-dropdown .dropdown-item:last-child:hover {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.snap-dropdown .dropdown-item.selected {
    background: #F3F4F6;
    color: var(--action-btn-bg);
}


/* Explicit tokenized styles for filter-type-dropdown to avoid inheritance ambiguities */
.filter-type-dropdown .dropdown-header {
    background: var(--bg-primary);
    border: 1.5px solid var(--border-color);
    color: var(--text-primary);
}
.filter-type-dropdown .dropdown-menu {
    background: var(--bg-primary);
    border: 1.5px solid var(--border-color);
}

/* Dark mode support for snap dropdown - matching dashboard style */
[data-theme="dark"] .snap-dropdown .dropdown-header,
.snap-grid.dark .snap-dropdown .dropdown-header,
body.dark .snap-dropdown .dropdown-header {
    background: var(--palette-gray-800, #1A1D23) !important;
    border: 1.5px solid var(--palette-gray-700, #2F3341) !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .snap-dropdown.focused .dropdown-header,
.snap-grid.dark .snap-dropdown.focused .dropdown-header,
body.dark .snap-dropdown.focused .dropdown-header {
    border-color: #470CED !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-menu,
.snap-grid.dark .snap-dropdown .dropdown-menu,
body.dark .snap-dropdown .dropdown-menu {
    background: var(--palette-gray-800, #1A1D23) !important;
    border: 1.5px solid var(--palette-gray-700, #2F3341) !important;
    scrollbar-color: #2F3341 transparent !important;
}

/* Dark mode scrollbar for dropdown menu */
[data-theme="dark"] .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb,
.snap-grid.dark .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb,
body.dark .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb {
    background: #2F3341 !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb:hover,
.snap-grid.dark .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb:hover,
body.dark .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #B4B9C5 !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-item,
.snap-grid.dark .snap-dropdown .dropdown-item,
body.dark .snap-dropdown .dropdown-item {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-item:hover,
.snap-grid.dark .snap-dropdown .dropdown-item:hover,
body.dark .snap-dropdown .dropdown-item:hover {
    background: var(--palette-gray-700, #2F3341) !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-item.selected,
.snap-grid.dark .snap-dropdown .dropdown-item.selected,
body.dark .snap-dropdown .dropdown-item.selected {
    background: #292E38 !important;
    color: #FFFFFF !important;
}

/* Column Management Tab Styling - Figma Design */
.column-management-divider {
    width: auto;
    height: 1.5px;
    background: var(--grid-border-color, #E9EBF2);
    margin: var(--spacing-xs, 4px) -16px;
    flex-shrink: 0;
}

[data-theme="dark"] .column-management-divider {
    background: var(--border-color) !important;
}

.pin-column-container {
    position: relative;
}

.pin-column-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 -16px; /* Extend hover area to edges */
    padding: 12px 16px; /* Add horizontal padding to compensate for margin */
    cursor: pointer;
    transition: background-color 0.15s ease;
    font-family: 'Amazon Ember', sans-serif;
    font-weight: 500;
    font-size: 14px;
    color: var(--color-text-primary, #18181b);
}

.pin-column-option:hover {
    background: var(--btn-hover, #F3F4F6);
}

/* Dark mode support for pin-column-option hover */
[data-theme="dark"] .pin-column-option:hover,
.snap-grid.dark .pin-column-option:hover,
body.dark .pin-column-option:hover {
    background: var(--palette-gray-700, #2F3341) !important;
}

.pin-column-main {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.pin-arrow {
    width: 10px;
    height: 10px;
    flex-shrink: 0;
}

.pin-submenu {
    position: absolute;
    top: 0;
    left: 100%;
    width: 130px;
    background: var(--color-surface-primary, #FFFFFF);
    border: 1.5px solid var(--grid-border-color, #E9EBF2);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: var(--z-dropdown, 1001);
    margin-left: 4px;
    padding: 16px 0;
    display: none; /* Hidden by default */
}

.pin-submenu:not(.hidden) {
    display: block;
}

[data-theme="dark"] .pin-submenu,
.snap-grid.dark .pin-submenu,
body.dark .pin-submenu {
    background: var(--palette-gray-800, #1A1D23) !important;
    border-color: var(--palette-gray-700, #2F3341) !important;
}

.pin-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.15s ease;
    font-family: 'Amazon Ember', sans-serif;
    font-weight: var(--grid-font-weight-medium, 500);
    font-size: 14px;
    color: #18181B;
}

.pin-option:hover {
    background: #F3F4F6;
}

[data-theme="dark"] .pin-option,
.snap-grid.dark .pin-option,
body.dark .pin-option {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .pin-option:hover,
.snap-grid.dark .pin-option:hover,
body.dark .pin-option:hover {
    background: var(--palette-gray-700, #2F3341) !important;
}

.check-icon {
    width: 12px;
    height: 12px;
    flex-shrink: 0;
}

/* Hidden utility class */
.hidden {
    visibility: hidden !important;
}

.reset-option {
    padding: 12px 16px 12px 42px; /* Same padding as hover state */
    margin: 0 -16px; /* Same margin as hover state */
    font-family: 'Amazon Ember', sans-serif;
    font-weight: var(--grid-font-weight-medium, 500);
    font-size: 14px;
    color: #18181B;
    cursor: pointer;
    transition: background-color 0.15s ease;
    background: transparent; /* Transparent background in normal state */
}

.reset-option:hover {
    background: var(--color-surface-hover, #F3F4F6);
}

[data-theme="dark"] .reset-option,
.snap-grid.dark .reset-option,
body.dark .reset-option {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .reset-option:hover {
    background: var(--btn-hover) !important;
}

/* Dark mode support for pin-column-option text color */
[data-theme="dark"] .pin-column-option,
.snap-grid.dark .pin-column-option,
body.dark .pin-column-option {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

/* Global Custom Input Styles */
.custom-input {
    width: 100%;
    padding: var(--spacing-sm, 8px) var(--spacing-md, 12px);
    background: var(--color-surface-secondary, #1f2937);
    border: 1px solid var(--grid-border-color, #374151);
    border-radius: var(--border-radius-sm, 4px);
    color: var(--color-text-primary, #ffffff);
    font-size: var(--font-size-sm, 14px);
    transition: all 0.15s ease;
}

.custom-input::placeholder {
    color: var(--color-text-secondary, #9ca3af);
}

.custom-input:focus {
    outline: none;
    border-color: var(--color-primary-500, #3b82f6);
    box-shadow: 0 0 0 3px var(--color-primary-100, rgba(59, 130, 246, 0.1));
}

.custom-input:hover:not(:focus) {
    border-color: var(--color-primary-400, #60a5fa);
}

/* Custom Checkbox Styles */
.custom-checkbox {
    display: flex;
    align-items: center;
    padding: var(--spacing-xs, 6px) var(--spacing-sm, 8px);
    cursor: pointer;
    transition: background-color 0.15s ease;
    border-radius: var(--border-radius-sm, 4px);
}

.custom-checkbox:hover {
    background: var(--color-surface-primary, #374151);
}

.custom-checkbox-input {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-sm, 8px);
    accent-color: var(--color-primary-500, #3b82f6);
    cursor: pointer;
}

.custom-checkbox-label {
    color: var(--color-text-primary, #ffffff);
    font-size: var(--font-size-sm, 14px);
    cursor: pointer;
    flex: 1;
    user-select: none;
}

/* ==========================================================================
   Grid Container & Layout
   ========================================================================== */

.snap-grid {
    --grid-border-color: var(--border-color);
    --grid-header-bg: var(--bg-secondary);
    --grid-row-bg: var(--bg-primary);
    --grid-row-hover-bg: var(--btn-hover);
    --grid-row-selected-bg: var(--color-light-gray);
    --grid-cell-padding: var(--spacing-sm, 12px);
    --grid-header-height: 48px;
    --grid-row-height: 40px;
    --grid-font-size: var(--font-size-sm, 14px);
    --grid-font-family: 'Amazon Ember', Arial, sans-serif;
    --grid-font-weight: 400;
    --grid-font-weight-medium: 500;
    --grid-font-weight-bold: 700;

    position: relative;
    width: 100%;
    min-height: calc(var(--grid-header-height) + (var(--grid-row-height) * 20));
    font-family: var(--grid-font-family);
    font-size: var(--grid-font-size);
    background: var(--grid-row-bg);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-md, 8px);
    overflow: hidden; /* Let viewport handle all scrolling */
    min-width: 100%; /* Ensure minimum width */

    /* Performance optimizations */
    contain: layout style;
    will-change: scroll-position;
}

/* Dark mode support */
[data-theme="dark"] .snap-grid,
.snap-grid.dark,
body.dark .snap-grid {
    --grid-border-color: var(--palette-gray-700, #2F3341);
    --grid-header-bg: var(--palette-gray-900, #111216);
    --grid-row-bg: var(--palette-gray-800, #1A1D23);
    --grid-row-hover-bg: var(--palette-gray-700, #2F3341);
    --grid-row-selected-bg: var(--palette-gray-900, #111216);
    --color-surface-primary: var(--palette-gray-800, #1A1D23);
    --color-surface-secondary: var(--palette-gray-900, #111216);
    --color-surface-tertiary: var(--palette-gray-700, #2F3341);
    --color-surface-hover: var(--palette-gray-700, #2F3341);
    --color-text-primary: var(--palette-gray-400, #B4B9C5);
    --color-text-secondary: var(--palette-gray-500, #606F95);
    --color-border-primary: var(--palette-gray-700, #2F3341);
    --color-primary-50: var(--palette-blue-primary, #7B8CFF);
    --color-primary-100: var(--palette-blue-dark, #5A6BFF);
    --color-primary-500: var(--palette-blue-primary, #7B8CFF);
    --color-primary-600: var(--palette-blue-primary, #7B8CFF);
    --color-primary-700: var(--palette-blue-dark, #5A6BFF);
}

/* Dark mode for dialogs */
[data-theme="dark"] .snap-grid-filter-dialog,
[data-theme="dark"] .snap-grid-column-chooser,
[data-theme="dark"] .snap-grid-column-menu,
[data-theme="dark"] .snap-grid-submenu-content,
.snap-grid.dark .snap-grid-filter-dialog,
.snap-grid.dark .snap-grid-column-chooser,
.snap-grid.dark .snap-grid-column-menu,
.snap-grid.dark .snap-grid-submenu-content,
body.dark .snap-grid-filter-dialog,
body.dark .snap-grid-column-chooser,
body.dark .snap-grid-column-menu,
body.dark .snap-grid-submenu-content {
    --color-surface-primary: var(--palette-gray-900, #111216);
    --color-surface-secondary: var(--palette-gray-800, #1A1D23);
    --color-surface-hover: var(--palette-gray-700, #2F3341);
    --color-text-primary: var(--palette-gray-400, #B4B9C5);
    --color-text-secondary: var(--palette-gray-500, #606F95);
    --color-border-primary: var(--palette-gray-700, #2F3341);
    --grid-border-color: var(--palette-gray-700, #2F3341);
    background: var(--color-surface-primary);
    color: var(--color-text-primary);
    border-color: var(--color-border-primary);
}

/* ==========================================================================
   Header Styles
   ========================================================================== */

.snap-grid-header {
    position: relative;
    background: var(--grid-header-bg);
    border-bottom: 2px solid var(--grid-border-color);
    display: flex; /* Layout pinned header sections and viewport side by side */
    overflow: hidden; /* Prevent content overflow */
    z-index: 10;
}

.snap-grid-header-pinned-left,
.snap-grid-header-pinned-right {
    position: relative;
    z-index: 1001;
    background: var(--grid-header-bg);
    overflow: hidden; /* Hide all scrollbars on pinned header sections */
    display: flex; /* Layout header cells horizontally */
}

.snap-grid-header-viewport {
    flex: 1; /* Take remaining space between pinned header sections */
    overflow-x: hidden; /* Hide scrollbar - sync with body viewport programmatically */
    overflow-y: visible; /* Allow menus to overflow vertically */
    background: var(--grid-header-bg);
}

/* WebKit: hide header scrollbar */
.snap-grid-header::-webkit-scrollbar {
    display: none;
}

.snap-grid-header-row {
    display: flex;
    min-width: max-content; /* Allow content to determine width for horizontal scrolling */
    height: var(--grid-header-height);
    overflow: visible; /* Allow menus to show outside */
    width: 100%; /* Ensure full width within header viewport */
}

.snap-grid-header-cell {
    position: relative;
    display: flex;
    align-items: center;
    flex: 0 0 auto; /* Prevent flex grow/shrink to maintain exact width */
    min-width: 0;
    padding: 0 var(--grid-cell-padding);
    background: var(--grid-header-bg);
    border-right: 1px solid var(--grid-border-color);
    font-weight: var(--grid-font-weight-medium, 500);
    color: var(--color-text-primary, #111827);
    user-select: none;
    cursor: pointer;
    transition: background-color 0.15s ease;
    box-sizing: border-box; /* Ensure consistent box model */
}

.snap-grid-header-cell:hover,
.snap-grid-header-cell.menu-active {
    background: var(--grid-row-hover-bg);
}

.snap-grid-header-cell:last-child {
    border-right: none;
}

.snap-grid-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 0;
    overflow: hidden; /* Prevent content from affecting cell width */
}

.snap-grid-header-title {
    flex: 1;
    min-width: 0;
    white-space: nowrap;
    /* No overflow hidden or ellipsis - headers are sized to fit content */
}

.snap-grid-sort-indicator {
    margin-left: var(--spacing-xs, 4px);
    width: 16px;
    height: 16px;
    transition: opacity 0.15s ease;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.snap-grid-sort-indicator.sorted-asc {
    opacity: 1;
    background-image: url('./assets/ascending-ic.svg');
}

.snap-grid-sort-indicator.sorted-desc {
    opacity: 1;
    background-image: url('./assets/descending-ic.svg');
}

/* Marketplace flag styling */
.marketplace-flag {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    vertical-align: middle;
    border-radius: 2px;
    object-fit: cover;
}

/* Checkbox column styling - specific to grid cells and headers */
.snap-grid-cell .checkbox-wrapper,
.snap-grid-header-content .checkbox-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.snap-grid-cell .checkbox-icon,
.snap-grid-header-content .checkbox-icon {
    width: 16px;
    height: 16px;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.snap-grid-cell .checkbox-icon:hover,
.snap-grid-header-content .checkbox-icon:hover {
    opacity: 0.8;
}

/* Preview square styling - product image placeholder */
.preview-square {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: var(--bg-secondary);
    cursor: pointer;
    transition: background-color 0.15s ease;
    margin: 0 auto;
}

.preview-square:hover {
    background: var(--btn-hover);
}

/* Actions column styling */
.actions-container {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
}

.action-btn {
    padding: 4px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    transition: background-color 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.action-btn:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

/* Dark mode support for new columns */
[data-theme="dark"] .preview-square {
    background: var(--bg-secondary);
}

[data-theme="dark"] .preview-square:hover {
    background: var(--btn-hover);
}

[data-theme="dark"] .action-btn:hover {
    background: var(--palette-gray-700, #2F3341);
}

.snap-grid-column-menu-btn {
    margin-left: var(--spacing-xs, 4px);
    padding: 2px 4px;
    background: none;
    border: none;
    border-radius: var(--border-radius-sm, 4px);
    font-size: 18px;
    color: var(--color-text-secondary, #6b7280);
    cursor: pointer;
    opacity: 0;
    transition: all 0.15s ease;
}

.snap-grid-header-cell:hover .snap-grid-column-menu-btn,
.snap-grid-header-cell.menu-active .snap-grid-column-menu-btn,
.snap-grid-header-cell:hover .snap-grid-column-menu-container .snap-grid-column-menu-btn,
.snap-grid-header-cell.menu-active .snap-grid-column-menu-container .snap-grid-column-menu-btn {
    opacity: 1;
}

.snap-grid-column-menu-btn:hover {
    background: var(--color-surface-hover, #f3f4f6);
    color: var(--color-text-primary, #111827);
}

/* Column menu container for positioning filter indicator */
.snap-grid-column-menu-container {
    position: relative;
    display: inline-block;
}

/* Filter indicator - small circle to show active filters */
.snap-grid-filter-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--color-primary-600, #470CED);
    pointer-events: none;
    z-index: 1;
}

/* Show filter indicator even when menu button is hidden */
.snap-grid-header-cell .snap-grid-filter-indicator {
    opacity: 1;
}

/* Dark mode support for filter indicator */
[data-theme="dark"] .snap-grid-filter-indicator,
.snap-grid.dark .snap-grid-filter-indicator,
body.dark .snap-grid-filter-indicator {
    background: var(--color-primary-500, #6366f1);
}

.snap-grid-resize-handle {
    position: absolute;
    top: 0;
    right: -1px;
    width: 2px;
    height: 100%;
    cursor: col-resize;
    background: transparent;
    transition: background-color 0.15s ease;
}

.snap-grid-resize-handle:hover {
    background: var(--color-primary-600, #470CED);
}

.snap-grid-resize-handle:active,
.snap-grid-resize-handle.resizing {
    background: var(--color-primary-600, #470CED);
}

/* Resize indicator line that appears during column resizing */
.snap-grid-resize-indicator {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--color-primary-600, #470CED);
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.1s ease;
}

.snap-grid-resize-indicator.active {
    opacity: 1;
}

/* ==========================================================================
   Body & Viewport Styles
   ========================================================================== */

.snap-grid-body {
    position: relative;
    height: calc(100% - var(--grid-header-height));
    display: flex; /* Layout pinned columns and viewport side by side */
    overflow: hidden; /* Prevent content overflow */
}

.snap-grid-pinned-left,
.snap-grid-pinned-right {
    position: relative;
    z-index: 1001;
    background: var(--grid-row-bg);
    overflow: hidden; /* Hide all scrollbars on pinned columns */
    height: 100%;
}

.snap-grid-pinned-right {
    border-right: none;
}

.snap-grid-pinned-row {
    display: flex;
    background: var(--grid-row-bg);
    border-bottom: 1px solid var(--grid-border-color);
    transition: background-color 0.15s ease;
}

.snap-grid-viewport {
    flex: 1; /* Take remaining space between pinned columns */
    height: 100%;
    overflow-x: auto; /* Single horizontal scrollbar at bottom */
    overflow-y: auto; /* Vertical scrollbar for rows */

    /* Performance optimizations for scrolling */
    contain: layout style paint;
    will-change: scroll-position;

    /* Floating/overlay scrollbars */
    scrollbar-width: thin; /* Firefox: thin scrollbar */
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent; /* Firefox: scrollbar colors */
}

/* WebKit browsers: floating scrollbars */
.snap-grid-viewport::-webkit-scrollbar {
    width: 8px; /* Vertical scrollbar width */
    height: 8px; /* Horizontal scrollbar height */
}

.snap-grid-viewport::-webkit-scrollbar-track {
    background: transparent; /* Transparent track */
}

.snap-grid-viewport::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3); /* Semi-transparent thumb */
    border-radius: 4px;
    border: none;
}

.snap-grid-viewport::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5); /* Darker on hover */
}

.snap-grid-viewport::-webkit-scrollbar-corner {
    background: transparent; /* Transparent corner */
}

/* Dark mode scrollbars */
.snap-grid.dark .snap-grid-viewport,
body.dark .snap-grid .snap-grid-viewport,
[data-theme="dark"] .snap-grid-viewport {
    scrollbar-color: #2F3341 transparent !important; /* Standardized dark mode scrollbar color */
}

.snap-grid.dark .snap-grid-viewport::-webkit-scrollbar-thumb,
body.dark .snap-grid .snap-grid-viewport::-webkit-scrollbar-thumb,
[data-theme="dark"] .snap-grid-viewport::-webkit-scrollbar-thumb {
    background: #2F3341 !important; /* Standardized dark mode scrollbar color */
}

.snap-grid.dark .snap-grid-viewport::-webkit-scrollbar-thumb:hover,
body.dark .snap-grid .snap-grid-viewport::-webkit-scrollbar-thumb:hover,
[data-theme="dark"] .snap-grid-viewport::-webkit-scrollbar-thumb:hover {
    background: #B4B9C5 !important; /* Standardized dark mode scrollbar hover color */
}

.snap-grid-viewport.smooth-scroll {
    scroll-behavior: smooth;
}

.snap-grid-canvas {
    position: relative;
    min-width: max-content; /* Allow content to determine width for horizontal scrolling */
    width: max-content; /* Ensure canvas is wide enough to trigger horizontal scrolling */
    /* Create a new containing block for sticky children */
    transform: translateZ(0);
}

/* ==========================================================================
   Row & Cell Styles
   ========================================================================== */

.snap-grid-row {
    display: flex;
    min-width: max-content; /* Allow content to determine width for horizontal scrolling */
    background: var(--grid-row-bg);
    border-bottom: 1px solid var(--grid-border-color);
    transition: background-color 0.15s ease;
    /* Allow horizontal sticky positioning while preventing vertical overflow issues */
    overflow-x: visible;
    overflow-y: hidden;
}

.snap-grid-row:hover {
    background: var(--grid-row-hover-bg);
}

.snap-grid-row.selected {
    background: var(--grid-row-selected-bg);
}

/* .snap-grid-row.odd - Background color removed - will use default row background */

.snap-grid-row.odd:hover {
    background: var(--grid-row-hover-bg);
}

.snap-grid-row.odd.selected {
    background: var(--grid-row-selected-bg);
}

/* Dark mode row fixes */
[data-theme="dark"] .snap-grid-row.odd {
    background: var(--palette-gray-900);
}

[data-theme="dark"] .snap-grid-row.odd:hover {
    background: var(--grid-row-hover-bg);
}

[data-theme="dark"] .snap-grid-row.odd.selected {
    background: var(--grid-row-selected-bg);
}

.snap-grid-cell {
    position: relative;
    display: flex;
    align-items: center;
    flex: 0 0 auto; /* Prevent flex grow/shrink to maintain exact width */
    min-width: 0;
    padding: 0 var(--grid-cell-padding);
    border-right: 1px solid var(--grid-border-color);
    color: var(--color-text-primary, #111827);
    transition: all 0.15s ease;
    box-sizing: border-box; /* Ensure consistent box model */
}

.snap-grid-cell:last-child {
    border-right: none;
}



.snap-grid-cell.editable {
    cursor: text;
}

.snap-grid-cell.editable:focus {
    outline: 2px solid var(--color-primary-500, #3b82f6);
    outline-offset: -2px;
}

.snap-grid-cell-content {
    width: 100%;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Text alignment utilities */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* ==========================================================================
   Cell Type Renderers
   ========================================================================== */

.snap-grid-boolean {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
}

.snap-grid-boolean.true {
    background: var(--color-success-100, #dcfce7);
    color: var(--color-success-700, #15803d);
}

.snap-grid-boolean.false {
    background: var(--color-error-100, #fee2e2);
    color: var(--color-error-700, #b91c1c);
}

.snap-grid-link {
    color: var(--color-primary-600, #2563eb);
    text-decoration: none;
    transition: color 0.15s ease;
}

.snap-grid-link:hover {
    color: var(--color-primary-700, #1d4ed8);
    text-decoration: underline;
}

/* ==========================================================================
   Column Menu Styles
   ========================================================================== */

.snap-grid-column-menu {
    background: var(--color-surface-primary, #ffffff);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-md, 8px);
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    padding: var(--spacing-xs, 4px) 0;
    min-width: 180px;
    z-index: 9999 !important;
}

/* Tabbed menu styles */
.snap-grid-column-menu.tabbed-menu {
    min-width: 281px;
    width: 281px;
    padding: 0;
    border: 1.5px solid var(--border-color);
    border-radius: 8px;
}

.menu-tab-header {
    display: flex;
    background: var(--bg-secondary);
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid var(--border-color);
    height: 48px;
}

.menu-tab-btn {
    flex: 1;
    padding: 12px;
    background: var(--bg-secondary);
    border: none;
    border-right: 1px solid var(--border-color);
    cursor: pointer;
    font-size: 16px;
    color: var(--text-secondary);
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    box-sizing: border-box;
}

.menu-tab-btn:first-child {
    border-top-left-radius: 8px;
}

.menu-tab-btn:last-child {
    border-right: none;
    border-top-right-radius: 8px;
}

.menu-tab-btn:hover {
    background: var(--btn-hover);
}

.menu-tab-btn.active {
    background: var(--bg-primary);
    position: relative;
}

.menu-tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--action-btn-bg);
}

.tab-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

/* Dark mode support for menu tabs */
[data-theme="dark"] .snap-grid-column-menu.tabbed-menu,
.snap-grid.dark .snap-grid-column-menu.tabbed-menu,
body.dark .snap-grid-column-menu.tabbed-menu {
    background: var(--palette-gray-800, #1A1D23) !important;
    border-color: var(--palette-gray-700, #2F3341) !important;
}

[data-theme="dark"] .menu-tab-header,
.snap-grid.dark .menu-tab-header,
body.dark .menu-tab-header {
    background: var(--palette-gray-900, #111216) !important;
    border-bottom-color: var(--palette-gray-700, #2F3341) !important;
}

[data-theme="dark"] .menu-tab-btn,
.snap-grid.dark .menu-tab-btn,
body.dark .menu-tab-btn {
    background: var(--palette-gray-800, #1A1D23) !important;
    border-color: var(--palette-gray-700, #2F3341) !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .menu-tab-btn:hover,
.snap-grid.dark .menu-tab-btn:hover,
body.dark .menu-tab-btn:hover {
    background: var(--palette-gray-700, #2F3341) !important;
}

[data-theme="dark"] .menu-tab-btn.active,
.snap-grid.dark .menu-tab-btn.active,
body.dark .menu-tab-btn.active {
    background: var(--palette-gray-900, #111216) !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
}

/* Dark mode support for tab icons - make active tab icons white */
[data-theme="dark"] .menu-tab-btn.active .tab-icon,
.snap-grid.dark .menu-tab-btn.active .tab-icon,
body.dark .menu-tab-btn.active .tab-icon {
    filter: brightness(0) invert(1) !important; /* Makes icons white */
}

/* Alternative approach using CSS variables if filter doesn't work */
[data-theme="dark"] .menu-tab-btn.active .tab-icon svg,
.snap-grid.dark .menu-tab-btn.active .tab-icon svg,
body.dark .menu-tab-btn.active .tab-icon svg {
    fill: var(--palette-white) !important;
    color: var(--palette-white) !important;
}

/* Dark mode support for inputs and dropdowns */
[data-theme="dark"] .filter-input-with-icon {
    background: var(--bg-primary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .search-input {
    background: var(--bg-primary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .column-search-input {
    background: var(--bg-primary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .pin-submenu {
    background: var(--palette-gray-800, #1A1D23) !important;
    border-color: var(--palette-gray-700, #2F3341) !important;
}

[data-theme="dark"] .filter-divider {
    background: var(--palette-gray-700, #2F3341) !important;
}

/* Dark mode support for menu options */
[data-theme="dark"] .menu-option-with-icon {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .menu-option-with-icon:hover {
    background: var(--palette-gray-700, #2F3341) !important;
}

[data-theme="dark"] .menu-option-label {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .snap-grid-menu-option {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .snap-grid-menu-option:hover {
    background: var(--palette-gray-700, #2F3341) !important;
}

/* Additional dark mode fixes for remaining elements */
[data-theme="dark"] .search-input-wrapper {
    background: transparent !important;
}

/* Dark mode support for icons - make them white */
[data-theme="dark"] .filter-icon,
[data-theme="dark"] .search-icon,
.snap-grid.dark .filter-icon,
.snap-grid.dark .search-icon,
body.dark .filter-icon,
body.dark .search-icon {
    filter: brightness(0) invert(1) !important; /* Makes icons white */
}

/* Alternative approach using CSS variables if filter doesn't work */
[data-theme="dark"] .filter-icon svg,
[data-theme="dark"] .search-icon svg,
.snap-grid.dark .filter-icon svg,
.snap-grid.dark .search-icon svg,
body.dark .filter-icon svg,
body.dark .search-icon svg {
    fill: var(--palette-white) !important;
    color: var(--palette-white) !important;
}

.menu-tab-content {
    padding: 16px;
}

.menu-tab-panel {
    display: none;
}

.menu-tab-panel.active {
    display: block;
}

/* Filter Tab Styles */
.filter-logic-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md, 16px);
}

.logic-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs, 8px);
    cursor: pointer;
    padding: var(--spacing-xs, 4px);
    border-radius: var(--border-radius-sm, 4px);
    transition: background-color 0.15s ease;
}

.logic-toggle:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

.logic-checkbox {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

.logic-label {
    font-size: var(--font-size-xs, 12px);
    font-weight: var(--grid-font-weight, 400);
    color: var(--color-text-primary, #000000);
}

.logic-separator {
    font-size: var(--font-size-xs, 12px);
    color: var(--color-text-secondary, #6b7280);
}

.filter-input-wrapper {
    position: relative;
}

.filter-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    object-fit: contain;
    pointer-events: none;
}

.filter-input-with-icon {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid var(--grid-border-color, #DCE0E5);
    border-radius: 4px;
    font-family: 'Amazon Ember', sans-serif;
    font-size: 12px;
    font-weight: var(--grid-font-weight-medium, 500);
    background: var(--color-surface-primary, #FFFFFF);
    color: var(--color-text-primary, #18181B);
    height: 40px;
    box-sizing: border-box;
}

.filter-input-with-icon::placeholder {
    color: var(--color-text-placeholder, #DCE0E5);
}

.filter-input-with-icon:focus {
    outline: none;
    border-color: var(--color-primary-600, #470CED);
    box-shadow: 0 0 0 1px var(--color-primary-600, #470CED);
}

.filter-divider {
    width: auto;
    height: 1.5px;
    background: var(--grid-border-color, #E9EBF2);
    margin-left: -16px;
    margin-right: -16px;
    flex-shrink: 0;
}

.search-input-wrapper {
    position: relative;
}

.search-icon {
    position: absolute;
    left: var(--spacing-sm, 12px);
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    object-fit: contain;
    pointer-events: none;
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm, 12px) var(--spacing-sm, 12px) var(--spacing-sm, 12px) 36px;
    border: 1px solid var(--grid-border-color, #dce0e5);
    border-radius: var(--border-radius-sm, 4px);
    font-size: var(--font-size-sm, 12px);
    background: var(--color-surface-primary, #ffffff);
    color: var(--color-text-primary, #18181b);
    box-sizing: border-box;
}

.search-input::placeholder {
    color: var(--color-text-placeholder, #dce0e5);
}

.search-input:focus {
    outline: none;
    border-color: var(--color-primary-600, #470ced);
    box-shadow: 0 0 0 1px var(--color-primary-600, #470ced);
}

/* Management Tab Styles */
.menu-option-with-icon {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 10px);
    padding: var(--spacing-sm, 12px) 16px var(--spacing-sm, 12px) 16px; /* Same padding as hover state */
    margin: 0 -16px; /* Same margin as hover state */
    cursor: pointer;
    transition: background-color 0.15s ease;
    background: transparent; /* Transparent background in normal state */
}

.menu-option-with-icon:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

.menu-option-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
}

.menu-option-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

.menu-option-label {
    font-size: var(--font-size-sm, 14px);
    font-weight: 500;
    color: var(--color-text-primary, #18181b);
}

/* Visibility Tab Styles */
.column-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md, 16px);
    max-height: 300px;
    overflow-y: auto;
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: #DCE0E5 transparent;
}

/* Webkit scrollbar styling for column list */
.column-list::-webkit-scrollbar {
    width: 6px;
}

.column-list::-webkit-scrollbar-track {
    background: transparent;
}

.column-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.column-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-primary);
}

/* Dark mode scrollbar for column list - standardized colors */
[data-theme="dark"] .column-list,
.snap-grid.dark .column-list,
body.dark .column-list {
    scrollbar-color: #2F3341 transparent !important;
}

[data-theme="dark"] .column-list::-webkit-scrollbar-thumb,
.snap-grid.dark .column-list::-webkit-scrollbar-thumb,
body.dark .column-list::-webkit-scrollbar-thumb {
    background: #2F3341 !important;
}

[data-theme="dark"] .column-list::-webkit-scrollbar-thumb:hover,
.snap-grid.dark .column-list::-webkit-scrollbar-thumb:hover,
body.dark .column-list::-webkit-scrollbar-thumb:hover {
    background: #B4B9C5 !important;
}

.column-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 10px);
    padding: var(--spacing-xs, 8px);
    border-radius: var(--border-radius-sm, 4px);
    transition: background-color 0.15s ease;
}

.column-item:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

.grip-handle {
    width: 16px;
    height: 16px;
    object-fit: contain;
    cursor: grab;
    opacity: 0.6;
    transition: opacity 0.15s ease;
}

.grip-handle:hover {
    opacity: 1;
}

.grip-handle:active {
    cursor: grabbing;
}

/* Drag and Drop Styles */
.column-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.column-item.drag-over {
    background: var(--color-primary-50, #f0f9ff);
    border: 1px dashed var(--color-primary-300, #93c5fd);
}

.drag-indicator {
    position: absolute;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--color-primary-600, #470ced);
    border-radius: 1px;
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.drag-indicator.active {
    opacity: 1;
}

.column-list {
    position: relative;
}

.column-item label {
    font-size: var(--font-size-sm, 14px);
    font-weight: 500;
    color: var(--color-text-primary, #18181b);
    cursor: pointer;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Remove old column-search-input styles - now using shared search-input styles */

/* Filter tab styles */
.menu-tab-panel[data-tab-panel="filter"].active {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm, 12px);
}

/* Filter tab specific styles */
.filter-search-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm, 8px);
}

.filter-checkbox-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs, 4px);
    max-height: 300px;
    overflow-y: auto;
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: #DCE0E5 transparent;
}

/* Webkit scrollbar styling */
.filter-checkbox-list::-webkit-scrollbar {
    width: 6px;
}

.filter-checkbox-list::-webkit-scrollbar-track {
    background: transparent;
}

.filter-checkbox-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.filter-checkbox-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-primary);
}

/* Dark mode scrollbar for filter checkbox list - standardized colors */
[data-theme="dark"] .filter-checkbox-list,
.snap-grid.dark .filter-checkbox-list,
body.dark .filter-checkbox-list {
    scrollbar-color: #2F3341 transparent !important;
}

[data-theme="dark"] .filter-checkbox-list::-webkit-scrollbar-thumb,
.snap-grid.dark .filter-checkbox-list::-webkit-scrollbar-thumb,
body.dark .filter-checkbox-list::-webkit-scrollbar-thumb {
    background: #2F3341 !important;
}

[data-theme="dark"] .filter-checkbox-list::-webkit-scrollbar-thumb:hover,
.snap-grid.dark .filter-checkbox-list::-webkit-scrollbar-thumb:hover,
body.dark .filter-checkbox-list::-webkit-scrollbar-thumb:hover {
    background: #B4B9C5 !important;
}

.filter-checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 10px);
    padding: var(--spacing-xs, 8px);
    border-radius: var(--border-radius-sm, 4px);
    transition: background-color 0.15s ease;
}

.filter-checkbox-item:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

.filter-checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.filter-checkbox-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

.filter-checkbox-item label {
    font-size: var(--font-size-xs, 12px);
    font-weight: 500;
    color: var(--color-text-primary, #18181b);
    cursor: pointer;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.filter-type-select,
.filter-input {
    width: 100%;
    padding: var(--spacing-sm, 8px);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-sm, 4px);
    font-size: var(--font-size-sm, 14px);
    background: var(--color-surface-primary, #ffffff);
    color: var(--color-text-primary, #111827);
}

.filter-type-select:focus,
.filter-input:focus {
    outline: 2px solid var(--color-primary-500, #3b82f6);
    outline-offset: -2px;
}

/* Column visibility tab styles */
.column-search-input {
    width: 100%;
    padding: var(--spacing-sm, 8px);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-sm, 4px);
    font-size: var(--font-size-sm, 14px);
    background: var(--color-surface-primary, #ffffff);
    color: var(--color-text-primary, #111827);
    margin-bottom: var(--spacing-sm, 12px);
}

.column-search-input:focus {
    outline: 2px solid var(--color-primary-500, #3b82f6);
    outline-offset: -2px;
}

.column-list {
    max-height: 200px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs, 4px);
}

.column-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-xs, 6px);
    cursor: pointer;
    border-radius: var(--border-radius-sm, 4px);
    transition: background-color 0.15s ease;
}

.column-item:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

.column-item input[type="checkbox"] {
    margin-right: var(--spacing-sm, 8px);
    cursor: pointer;
}

/* Checkbox wrapper styling for column visibility */
.column-item .checkbox-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    cursor: pointer;
    flex-shrink: 0;
}

.column-item .checkbox-wrapper img {
    width: 16px;
    height: 16px;
    transition: opacity 0.2s ease;
}

/* Global checkbox styling for unchecked state - matches snap image studio */
.checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 0.2;
}

/* Dark mode overrides - higher specificity to ensure they take precedence */
[data-theme="dark"] .checkbox-wrapper img[src*="uncheckedbox-ic"],
body.dark .checkbox-wrapper img[src*="uncheckedbox-ic"],
.snap-grid.dark .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 1 !important;
}

/* Column item checkbox styling for unchecked state */
.column-item .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 0.2;
}

[data-theme="dark"] .column-item .checkbox-wrapper img[src*="uncheckedbox-ic"],
body.dark .column-item .checkbox-wrapper img[src*="uncheckedbox-ic"],
.snap-grid.dark .column-item .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 1 !important;
}

/* Filter checkbox styling for unchecked state */
.filter-checkbox-item .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 0.2;
}

[data-theme="dark"] .filter-checkbox-item .checkbox-wrapper img[src*="uncheckedbox-ic"],
body.dark .filter-checkbox-item .checkbox-wrapper img[src*="uncheckedbox-ic"],
.snap-grid.dark .filter-checkbox-item .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 1 !important;
}

/* Grid header checkbox styling for unchecked state */
.snap-grid-header-content .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 0.2;
}

[data-theme="dark"] .snap-grid-header-content .checkbox-wrapper img[src*="uncheckedbox-ic"],
body.dark .snap-grid-header-content .checkbox-wrapper img[src*="uncheckedbox-ic"],
.snap-grid.dark .snap-grid-header-content .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 1 !important;
}

.column-item label {
    cursor: pointer;
    font-size: var(--font-size-xs, 12px);
    color: var(--color-text-primary, #111827);
}

.snap-grid-menu-option {
    padding: var(--spacing-sm, 8px) var(--spacing-md, 16px);
    cursor: pointer;
    color: var(--color-text-primary, #111827);
    transition: background-color 0.15s ease;
}

.snap-grid-menu-option:hover {
    background: var(--grid-row-hover-bg);
}

.snap-grid-menu-separator {
    height: 1px;
    background: var(--grid-border-color);
    margin: var(--spacing-xs, 4px) 0;
}

/* Submenu styles */
.snap-grid-menu-submenu {
    position: relative;
}

.submenu-trigger {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.submenu-arrow {
    font-size: 10px;
    transition: transform 0.15s ease;
    color: inherit; /* Inherit text color from parent */
}

/* Dark mode support for submenu arrow */
[data-theme="dark"] .submenu-arrow,
.snap-grid.dark .submenu-arrow,
body.dark .submenu-arrow {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

.snap-grid-submenu-content {
    position: absolute;
    left: 100%;
    top: 0;
    background: var(--color-surface-primary, #ffffff);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-md, 8px);
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    min-width: 140px;
    z-index: 1001;
    display: none;
}

.snap-grid-menu-option.checked {
    background: var(--color-primary-50, #eff6ff);
    color: var(--color-primary-700, #1d4ed8);
}

/* Dark mode support for checked menu options */
[data-theme="dark"] .snap-grid-menu-option.checked,
.snap-grid.dark .snap-grid-menu-option.checked,
body.dark .snap-grid-menu-option.checked {
    background: rgba(123, 140, 255, 0.1) !important; /* Light blue with transparency for dark mode */
    color: var(--palette-blue-light, #9BA8FF) !important;
}

/* ==========================================================================
   Filter Dialog Styles
   ========================================================================== */

.snap-grid-filter-dialog {
    background: var(--color-surface-primary, #ffffff);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-lg, 12px);
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
    min-width: 400px;
    max-width: 500px;
    z-index: 1001;
}

.filter-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg, 20px);
    border-bottom: 1px solid var(--grid-border-color);
}

.filter-dialog-header h3 {
    margin: 0;
    font-size: var(--font-size-lg, 18px);
    font-weight: var(--font-weight-semibold, 600);
    color: var(--color-text-primary, #111827);
}

.filter-dialog-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--color-text-secondary, #6b7280);
    cursor: pointer;
    padding: var(--spacing-xs, 4px);
    border-radius: var(--border-radius-sm, 4px);
    transition: all 0.15s ease;
}

.filter-dialog-close:hover {
    background: var(--color-surface-hover, #f3f4f6);
    color: var(--color-text-primary, #111827);
}

.filter-dialog-body {
    padding: var(--spacing-lg, 20px);
}

.filter-type-section,
.filter-value-section {
    margin-bottom: var(--spacing-md, 16px);
}

.filter-type-section label,
.filter-value-section label {
    display: block;
    margin-bottom: var(--spacing-xs, 6px);
    font-weight: var(--font-weight-medium, 500);
    color: var(--color-text-primary, #111827);
}

.filter-type-select,
.filter-value-input {
    width: 100%;
    padding: var(--spacing-sm, 10px) var(--spacing-md, 12px);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-md, 6px);
    font-size: var(--font-size-sm, 14px);
    background: var(--color-surface-primary, #ffffff);
    color: var(--color-text-primary, #111827);
    transition: border-color 0.15s ease;
}

.filter-type-select:focus,
.filter-value-input:focus {
    outline: none;
    border-color: var(--color-primary-500, #3b82f6);
    box-shadow: 0 0 0 3px var(--color-primary-100, #dbeafe);
}

.filter-value-input:disabled {
    background: var(--color-surface-secondary, #f9fafb);
    color: var(--color-text-secondary, #6b7280);
    cursor: not-allowed;
}

.filter-actions {
    display: flex;
    gap: var(--spacing-sm, 8px);
    justify-content: flex-end;
    margin-top: var(--spacing-lg, 20px);
}

.btn {
    padding: var(--spacing-sm, 8px) var(--spacing-md, 16px);
    border: none;
    border-radius: var(--border-radius-md, 6px);
    font-size: var(--font-size-sm, 14px);
    font-weight: var(--font-weight-medium, 500);
    cursor: pointer;
    transition: all 0.15s ease;
}

.btn-primary {
    background: var(--color-primary-600, #2563eb);
    color: white;
}

.btn-primary:hover {
    background: var(--color-primary-700, #1d4ed8);
}

.btn-secondary {
    background: var(--color-surface-secondary, #f9fafb);
    color: var(--color-text-primary, #111827);
    border: 1px solid var(--grid-border-color);
}

.btn-secondary:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

/* ==========================================================================
   Column Chooser Styles
   ========================================================================== */

.snap-grid-column-chooser {
    background: var(--color-surface-primary, #ffffff);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-lg, 12px);
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
    min-width: 350px;
    max-width: 450px;
    max-height: 500px;
    z-index: 1001;
}

.column-chooser-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg, 20px);
    border-bottom: 1px solid var(--grid-border-color);
}

.column-chooser-header h3 {
    margin: 0;
    font-size: var(--font-size-lg, 18px);
    font-weight: var(--font-weight-semibold, 600);
    color: var(--color-text-primary, #111827);
}

.column-chooser-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--color-text-secondary, #6b7280);
    cursor: pointer;
    padding: var(--spacing-xs, 4px);
    border-radius: var(--border-radius-sm, 4px);
    transition: all 0.15s ease;
}

.column-chooser-close:hover {
    background: var(--color-surface-hover, #f3f4f6);
    color: var(--color-text-primary, #111827);
}

.column-chooser-body {
    padding: var(--spacing-lg, 20px);
}

.column-list {
    max-height: 300px;
    overflow-y: auto;
    margin-top: var(--spacing-md, 16px);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-md, 6px);
    padding: var(--spacing-sm, 8px);
}

.column-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm, 8px);
    cursor: pointer;
    border-radius: var(--border-radius-sm, 4px);
    transition: background-color 0.15s ease;
}

.column-item:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

.column-item input[type="checkbox"] {
    margin-right: var(--spacing-sm, 8px);
}

.column-chooser-actions {
    display: flex;
    gap: var(--spacing-sm, 8px);
    justify-content: space-between;
}

.column-chooser-actions .btn {
    flex: 1;
}

/* ==========================================================================
   Column Pinning Styles
   ========================================================================== */

.snap-grid-header-cell.pinned-left,
.snap-grid-cell.pinned-left {
    position: sticky !important;
    left: 0 !important;
    z-index: 1001 !important;
    background: var(--grid-header-bg) !important;
    border-right: 3px solid var(--grid-border-color);
}

.snap-grid-header-cell.pinned-right,
.snap-grid-cell.pinned-right {
    position: sticky !important;
    right: 0 !important;
    z-index: 1001 !important;
    background: var(--grid-header-bg) !important;
    border-left: 3px solid var(--grid-border-color);
}

.snap-grid-cell.pinned-left,
.snap-grid-cell.pinned-right {
    background: var(--grid-row-bg) !important;
}

.snap-grid-row:hover .snap-grid-cell.pinned-left,
.snap-grid-row:hover .snap-grid-cell.pinned-right {
    background: var(--grid-row-hover-bg) !important;
}

.snap-grid-row.selected .snap-grid-cell.pinned-left,
.snap-grid-row.selected .snap-grid-cell.pinned-right {
    background: var(--grid-row-selected-bg) !important;
}

/* Dark mode pinned columns */
.snap-grid.dark .snap-grid-header-cell.pinned-left,
.snap-grid.dark .snap-grid-header-cell.pinned-right,
body.dark .snap-grid .snap-grid-header-cell.pinned-left,
body.dark .snap-grid .snap-grid-header-cell.pinned-right {
    background: var(--grid-header-bg);
    border-color: var(--grid-border-color);
}

.snap-grid.dark .snap-grid-cell.pinned-left,
.snap-grid.dark .snap-grid-cell.pinned-right,
body.dark .snap-grid .snap-grid-cell.pinned-left,
body.dark .snap-grid .snap-grid-cell.pinned-right {
    background: var(--grid-row-bg);
}

/* ==========================================================================
   Group Header Styles
   ========================================================================== */

.snap-grid-group-row {
    background: var(--color-surface-secondary, #f9fafb);
    border-bottom: 2px solid var(--grid-border-color);
    font-weight: var(--font-weight-semibold, 600);
    color: var(--color-text-primary, #111827);
    height: var(--grid-row-height); /* Tie to grid row height for consistent virtualization */
}

.snap-grid-group-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm, 8px) var(--spacing-md, 12px);
    cursor: pointer;
    user-select: none;
}

.snap-grid-group-toggle {
    margin-right: var(--spacing-sm, 8px);
    font-size: 12px;
    transition: transform 0.15s ease;
}

.snap-grid-group-toggle.expanded {
    transform: rotate(90deg);
}

.snap-grid-group-title {
    flex: 1;
    font-weight: var(--font-weight-semibold, 600);
}

.snap-grid-group-count {
    color: var(--color-text-secondary, #6b7280);
    font-size: var(--font-size-sm, 14px);
    margin-left: var(--spacing-sm, 8px);
}

.snap-grid-group-row:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

/* Dark mode group headers */
.snap-grid.dark .snap-grid-group-row,
body.dark .snap-grid .snap-grid-group-row {
    background: var(--color-surface-secondary, #1f2937);
    color: var(--color-text-primary, #f9fafb);
}

.snap-grid.dark .snap-grid-group-row:hover,
body.dark .snap-grid .snap-grid-group-row:hover {
    background: var(--color-surface-hover, #374151);
}

/* ==========================================================================
   Footer & Pagination Styles
   ========================================================================== */

.snap-grid-footer {
    background: var(--grid-header-bg);
    border-top: 1px solid var(--grid-border-color);
    padding: var(--spacing-sm, 8px) var(--spacing-md, 16px);
}

.snap-grid-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-sm, 14px);
}

.pagination-info {
    color: var(--color-text-secondary, #6b7280);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 8px);
}

.pagination-btn {
    padding: var(--spacing-xs, 4px) var(--spacing-sm, 8px);
    background: var(--color-surface-primary, #ffffff);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-sm, 4px);
    font-size: var(--font-size-sm, 14px);
    color: var(--color-text-primary, #111827);
    cursor: pointer;
    transition: all 0.15s ease;
}

.pagination-btn:hover {
    background: var(--grid-row-hover-bg);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-pages {
    color: var(--color-text-secondary, #6b7280);
    font-weight: var(--font-weight-medium, 500);
}

/* ==========================================================================
   Overlay & Error Styles
   ========================================================================== */

.snap-grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.snap-grid-error {
    background: var(--color-surface-primary, #ffffff);
    border: 1px solid var(--color-error-300, #fca5a5);
    border-radius: var(--border-radius-lg, 12px);
    padding: var(--spacing-lg, 24px);
    max-width: 400px;
    text-align: center;
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
}

.error-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md, 16px);
}

.error-message {
    color: var(--color-error-700, #b91c1c);
    font-weight: var(--font-weight-medium, 500);
    margin-bottom: var(--spacing-md, 16px);
}

.error-close {
    position: absolute;
    top: var(--spacing-sm, 8px);
    right: var(--spacing-sm, 8px);
    background: none;
    border: none;
    font-size: 20px;
    color: var(--color-text-secondary, #6b7280);
    cursor: pointer;
    padding: var(--spacing-xs, 4px);
    border-radius: var(--border-radius-sm, 4px);
    transition: all 0.15s ease;
}

.error-close:hover {
    background: var(--grid-row-hover-bg);
    color: var(--color-text-primary, #111827);
}

/* ==========================================================================
   Accessibility Styles
   ========================================================================== */

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators */
.snap-grid:focus {
    outline: 2px solid var(--color-primary-500, #3b82f6);
    outline-offset: 2px;
}

.snap-grid-header-cell:focus,
.snap-grid-cell:focus {
    outline: 2px solid var(--color-primary-500, #3b82f6);
    outline-offset: -2px;
    z-index: 1;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .snap-grid {
        --grid-border-color: #000000;
        border-width: 2px;
    }

    .snap-grid-row:hover {
        background: #f0f0f0;
    }

    .snap-grid-cell.selected {
        background: #0066cc;
        color: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .snap-grid,
    .snap-grid-header-cell,
    .snap-grid-row,
    .snap-grid-cell,
    .snap-grid-column-menu-btn,
    .snap-grid-resize-handle,
    .pagination-btn {
        transition: none;
    }


}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

/* Tablet styles */
@media (max-width: 768px) {
    .snap-grid {
        --grid-cell-padding: var(--spacing-xs, 8px);
        --grid-font-size: var(--font-size-xs, 12px);
        --grid-row-height: 36px;
        --grid-header-height: 44px;
    }

    .snap-grid-header-cell {
        min-width: 120px;
    }

    .snap-grid-cell {
        min-width: 120px;
    }

    .snap-grid-column-menu-btn {
        opacity: 1; /* Always visible on touch devices */
    }

    .pagination-controls {
        gap: var(--spacing-xs, 4px);
    }

    .pagination-btn {
        padding: var(--spacing-xs, 4px) var(--spacing-xs, 6px);
        font-size: var(--font-size-xs, 12px);
    }
}

/* Mobile styles */
@media (max-width: 480px) {
    .snap-grid {
        --grid-cell-padding: var(--spacing-xs, 6px);
        --grid-font-size: var(--font-size-xs, 11px);
        --grid-row-height: 32px;
        --grid-header-height: 40px;
        border-radius: var(--border-radius-sm, 4px);
    }

    .snap-grid-header-cell {
        min-width: 100px;
    }

    .snap-grid-cell {
        min-width: 100px;
    }

    .snap-grid-pagination {
        flex-direction: column;
        gap: var(--spacing-sm, 8px);
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }

    .snap-grid-column-menu {
        min-width: 160px;
        font-size: var(--font-size-xs, 12px);
    }

    .snap-grid-menu-option {
        padding: var(--spacing-xs, 6px) var(--spacing-sm, 12px);
    }

    /* Tabbed menu mobile adjustments */
    .snap-grid-column-menu.tabbed-menu {
        min-width: 260px;
    }

    .menu-tab-btn {
        padding: var(--spacing-xs, 8px);
        font-size: 14px;
    }

    .menu-tab-content {
        padding: var(--spacing-sm, 12px);
    }

    .column-list {
        max-height: 150px;
    }
}

/* ==========================================================================
   Performance Optimizations
   ========================================================================== */

/* GPU acceleration for smooth scrolling */
.snap-grid-viewport {
    transform: translateZ(0);
    -webkit-overflow-scrolling: touch;
}

/* Removed transform: translateZ(0) from .snap-grid-row to prevent conflicts with pinned columns */

/* Optimize repaints during virtual scrolling */
.snap-grid-canvas {
    contain: layout style paint;
}

/* Optimize column resizing */
.snap-grid-header-cell {
    contain: layout style;
}

/* ==========================================================================
   Loading & Skeleton Styles
   ========================================================================== */

.snap-grid-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.snap-grid-skeleton-row {
    display: flex;
    height: var(--grid-row-height);
    border-bottom: 1px solid var(--grid-border-color);
}

.snap-grid-skeleton-cell {
    flex: 1;
    padding: var(--grid-cell-padding);
    border-right: 1px solid var(--grid-border-color);
}

.snap-grid-skeleton-content {
    height: 16px;
    background: linear-gradient(
        90deg,
        var(--color-surface-tertiary, #f1f5f9) 25%,
        var(--color-surface-secondary, #e2e8f0) 50%,
        var(--color-surface-tertiary, #f1f5f9) 75%
    );
    background-size: 200% 100%;
    border-radius: var(--border-radius-sm, 4px);
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .snap-grid {
        border: 1px solid #000;
        box-shadow: none;
    }

    .snap-grid-header {
        background: #f5f5f5 !important;
    }

    .snap-grid-row:nth-child(even) {
        background: #f9f9f9 !important;
    }

    .snap-grid-column-menu-btn,
    .snap-grid-resize-handle,
    .snap-grid-footer {
        display: none !important;
    }

    .snap-grid-viewport {
        overflow: visible !important;
        height: auto !important;
    }

    .snap-grid-canvas {
        height: auto !important;
    }

    .snap-grid-row {
        position: static !important;
        transform: none !important;
    }
}

/* ==========================================================================
   Theme Variations
   ========================================================================== */

/* Compact theme */
.snap-grid.compact {
    --grid-row-height: 32px;
    --grid-header-height: 40px;
    --grid-cell-padding: var(--spacing-xs, 8px);
    --grid-font-size: var(--font-size-xs, 12px);
}

/* Dense theme */
.snap-grid.dense {
    --grid-row-height: 28px;
    --grid-header-height: 36px;
    --grid-cell-padding: var(--spacing-xs, 6px);
    --grid-font-size: var(--font-size-xs, 11px);
}

/* Comfortable theme */
.snap-grid.comfortable {
    --grid-row-height: 48px;
    --grid-header-height: 56px;
    --grid-cell-padding: var(--spacing-md, 16px);
    --grid-font-size: var(--font-size-base, 16px);
}



/* Token enforcement override for dropdown header (temporary for cache/specificity issues) */
.snap-dropdown .dropdown-header {
    background: var(--bg-primary) !important;
    border: 1.5px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* Drag indicator styling */
.column-list { position: relative; }
.column-list .drag-indicator {
    position: absolute;
    height: 2px;
    background-color: #3B82F6; /* blue */
    left: 0;
    right: 0;
    pointer-events: none;
    transform: translateY(-1px);
}

/* Custom date picker icon styling */
.filter-input-with-icon[type="date"]::-webkit-calendar-picker-indicator {
    background-image: url('./assets/date-picker-ic.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 12px 12px;
    width: 12px;
    height: 12px;
    cursor: pointer;
    opacity: 1;
}

.filter-input-with-icon[type="date"]::-moz-calendar-picker-indicator {
    background-image: url('./assets/date-picker-ic.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 12px 12px;
    width: 12px;
    height: 12px;
    cursor: pointer;
    opacity: 1;
    border: none;
    background-color: transparent;
}

/* Dark mode support for date picker icon - make it white */
[data-theme="dark"] .filter-input-with-icon[type="date"]::-webkit-calendar-picker-indicator,
.snap-grid.dark .filter-input-with-icon[type="date"]::-webkit-calendar-picker-indicator,
body.dark .filter-input-with-icon[type="date"]::-webkit-calendar-picker-indicator {
    filter: brightness(0) invert(1) !important; /* Makes icon white */
}

[data-theme="dark"] .filter-input-with-icon[type="date"]::-moz-calendar-picker-indicator,
.snap-grid.dark .filter-input-with-icon[type="date"]::-moz-calendar-picker-indicator,
body.dark .filter-input-with-icon[type="date"]::-moz-calendar-picker-indicator {
    filter: brightness(0) invert(1) !important; /* Makes icon white */
}
